$trans-cubic-bezier: cubic-bezier(0.215, 0.61, 0.355, 1);
@mixin timing-function {
  animation-timing-function: $trans-cubic-bezier;
}

/* ----------------------------------------------
 * Modified version from Animista
 * Animista is Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@keyframes slideInBlurredLeft {
  0% {
    transform: translateX(-1000px) scaleX(2.5) scaleY(0.2);
    transform-origin: 100% 50%;
    filter: blur(40px);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}

@keyframes slideInBlurredTop {
  0% {
    transform: translateY(-1000px) scaleY(2.5) scaleX(0.2);
    transform-origin: 50% 0%;
    filter: blur(240px);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}

@keyframes slideInBlurredRight {
  0% {
    transform: translateX(1000px) scaleX(2.5) scaleY(0.2);
    transform-origin: 0% 50%;
    filter: blur(40px);
    opacity: 0;
  }
  100% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}

@keyframes slideInBlurredBottom {
  0% {
    transform: translateY(1000px) scaleY(2.5) scaleX(0.2);
    transform-origin: 50% 100%;
    filter: blur(240px);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
}

@keyframes slideOutBlurredTop {
  0% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 0%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-1000px) scaleY(2) scaleX(0.2);
    transform-origin: 50% 0%;
    filter: blur(240px);
    opacity: 0;
  }
}

@keyframes slideOutBlurredBottom {
  0% {
    transform: translateY(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateY(1000px) scaleY(2) scaleX(0.2);
    transform-origin: 50% 100%;
    filter: blur(240px);
    opacity: 0;
  }
}

@keyframes slideOutBlurredLeft {
  0% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-1000px) scaleX(2) scaleY(0.2);
    transform-origin: 100% 50%;
    filter: blur(40px);
    opacity: 0;
  }
}

@keyframes slideOutBlurredRight {
  0% {
    transform: translateX(0) scaleY(1) scaleX(1);
    transform-origin: 50% 50%;
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: translateX(1000px) scaleX(2) scaleY(0.2);
    transform-origin: 0% 50%;
    filter: blur(40px);
    opacity: 0;
  }
}

.#{$vt-namespace}__slideBlurred-enter-active {
  &.top-left,
  &.bottom-left {
    animation-name: slideInBlurredLeft;
  }
  &.top-right,
  &.bottom-right {
    animation-name: slideInBlurredRight;
  }
  &.top-center {
    animation-name: slideInBlurredTop;
  }
  &.bottom-center {
    animation-name: slideInBlurredBottom;
  }
}

.#{$vt-namespace}__slideBlurred-leave-active:not(.disable-transition) {
  &.top-left,
  &.bottom-left {
    animation-name: slideOutBlurredLeft;
  }
  &.top-right,
  &.bottom-right {
    animation-name: slideOutBlurredRight;
  }
  &.top-center {
    animation-name: slideOutBlurredTop;
  }
  &.bottom-center {
    animation-name: slideOutBlurredBottom;
  }
}

.#{$vt-namespace}__slideBlurred-leave-active,
.#{$vt-namespace}__slideBlurred-enter-active {
  animation-duration: 750ms;
  animation-fill-mode: both;
}

.#{$vt-namespace}__slideBlurred-move {
  transition-timing-function: ease-in-out;
  transition-property: all;
  transition-duration: 400ms;
}
