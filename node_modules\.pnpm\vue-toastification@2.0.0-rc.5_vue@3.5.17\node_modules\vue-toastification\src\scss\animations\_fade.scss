$trans-cubic-bezier: cubic-bezier(0.215, 0.61, 0.355, 1);
@mixin timing-function {
  animation-timing-function: $trans-cubic-bezier;
}

/* ----------------------------------------------
 * Modified version from Animista
 * Animista is Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@keyframes fadeOutTop {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-50px);
    opacity: 0;
  }
}

@keyframes fadeOutLeft {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50px);
    opacity: 0;
  }
}

@keyframes fadeOutBottom {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(50px);
    opacity: 0;
  }
}

@keyframes fadeOutRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(50px);
    opacity: 0;
  }
}

@keyframes fadeInLeft {
  0% {
    transform: translateX(-50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInRight {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInTop {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInBottom {
  0% {
    transform: translateY(50px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.#{$vt-namespace}__fade-enter-active {
  &.top-left,
  &.bottom-left {
    animation-name: fadeInLeft;
  }
  &.top-right,
  &.bottom-right {
    animation-name: fadeInRight;
  }
  &.top-center {
    animation-name: fadeInTop;
  }
  &.bottom-center {
    animation-name: fadeInBottom;
  }
}

.#{$vt-namespace}__fade-leave-active:not(.disable-transition) {
  &.top-left,
  &.bottom-left {
    animation-name: fadeOutLeft;
  }
  &.top-right,
  &.bottom-right {
    animation-name: fadeOutRight;
  }
  &.top-center {
    animation-name: fadeOutTop;
  }
  &.bottom-center {
    animation-name: fadeOutBottom;
  }
}

.#{$vt-namespace}__fade-leave-active,
.#{$vt-namespace}__fade-enter-active {
  animation-duration: 750ms;
  animation-fill-mode: both;
}

.#{$vt-namespace}__fade-move {
  transition-timing-function: ease-in-out;
  transition-property: all;
  transition-duration: 400ms;
}
